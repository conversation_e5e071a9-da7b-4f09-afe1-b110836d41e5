{"version": 3, "file": "login.js", "sources": ["pages/login/login.vue", "C:/software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbG9naW4vbG9naW4udnVl"], "sourcesContent": ["<template>\n  <view class=\"login-container\">\n    <!-- 顶部logo区域 -->\n    <view class=\"logo-section\">\n      <image class=\"logo\" src=\"/static/logo.png\" mode=\"aspectFit\"></image>\n      <view class=\"app-name\">华泰民商调解中心</view>\n      <!-- <view class=\"app-desc\">专业的不良资产处置平台</view> -->\n    </view>\n\n    <!-- 登录表单区域 -->\n    <view class=\"login-form\">\n      <!-- 微信登录按钮 -->\n      <button \n        class=\"wechat-login-btn\"\n        :disabled=\"isLogging\"\n        @click=\"handleWechatLogin\"\n      >\n        <text class=\"wechat-icon\">&#xe65f;</text>\n        <text class=\"login-text\">\n          {{ isLogging ? '登录中...' : '微信快速登录' }}\n        </text>\n      </button>\n\n      <!-- <view class=\"other-login\">\n        <view class=\"divider\">\n          <text class=\"divider-text\">其他登录方式</text>\n        </view>\n        \n        <button \n          class=\"phone-login-btn\"\n          @click=\"showPhoneLogin = true\"\n        >\n          <text class=\"phone-icon\">&#xe60c;</text>\n          <text>手机号登录</text>\n        </button>\n      </view> -->\n    </view>\n\n    <!-- 底部协议 -->\n    <view class=\"agreement-section\">\n      <view class=\"agreement-text\">\n         <checkbox-group @change=\"toggleAgreement\">\n          <checkbox :checked=\"isAgreed\" color=\"#2979FF\" />\n        </checkbox-group>\n        我已阅读并同意遵循<text class=\"link\" @click=\"showUserAgreement\">《用户服务协议》</text>和<text class=\"link\" @click=\"showPrivacyPolicy\">《个人信息保护政策》</text>\n      </view>\n    </view>\n\n\n    <!-- 加载遮罩 -->\n    <view class=\"loading-mask\" v-if=\"isLogging\">\n      <view class=\"loading-content\">\n        <uni-load-more status=\"loading\" :content-text=\"loadingText\"></uni-load-more>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue';\nimport {\n  checkAndHandleLoginStatus,\n  showLoginError,\n  LOGIN_RESULT_TYPE\n} from '@/utils/login-helper.js';\nimport { api } from '@/utils/api.js';\nimport { navigate } from '@/config/routes.js';\nimport wechatAuth from '@/utils/wechat-auth.js';\nimport userStore from '@/utils/user-store.js';\n\n// 响应式数据\nconst isLogging = ref(false);\nconst isPhoneLogging = ref(false);\nconst showPhoneLogin = ref(false);\nconst countdown = ref(0);\n\n// 手机登录表单\nconst phoneForm = ref({\n  phone: '',\n  code: ''\n});\n\n// 加载文案\nconst loadingText = ref({\n  contentText: {\n    contentdown: '正在登录...',\n    contentrefresh: '正在登录...',\n    contentnomore: '登录完成'\n  }\n});\n\nconst isAgreed = ref(false);\n\nconst toggleAgreement = () => {\n  isAgreed.value = !isAgreed.value;\n};\n\n// 计算属性\nconst canSendCode = computed(() => {\n  return phoneForm.value.phone.length === 11 && countdown.value === 0;\n});\n\nconst canSubmitPhone = computed(() => {\n  return phoneForm.value.phone.length === 11 && \n         phoneForm.value.code.length === 6 && \n         !isPhoneLogging.value;\n});\n\nconst codeButtonText = computed(() => {\n  return countdown.value > 0 ? `${countdown.value}s` : '获取验证码';\n});\n\n// 页面加载\nonMounted(() => {\n  // 检查是否已经登录\n  checkLoginStatus();\n});\n\n// 检查登录状态 - 使用新的登录辅助工具\nasync function checkLoginStatus() {\n  try {\n    const result = await checkAndHandleLoginStatus({\n      autoLogin: false,    // 页面加载时不自动登录\n      autoNavigate: true,  // 自动导航\n      showLoading: false   // 不显示加载提示\n    });\n\n    if (result.success && result.isLogin) {\n      // 已登录，自动导航逻辑已在 checkAndHandleLoginStatus 中处理\n      console.log('用户已登录，自动跳转');\n    }\n  } catch (error) {\n    console.error('检查登录状态失败:', error);\n  }\n}\n\n// 微信登录 - 简化版：一键登录，使用固定默认值\nasync function handleWechatLogin() {\n  if (isLogging.value) return;\n\n  if (!isAgreed.value) {\n    uni.showToast({\n      title: '请先阅读并勾选《用户服务协议》和《个人信息保护政策》',\n      icon: 'none'\n    });\n    return;\n  }\n\n  isLogging.value = true;\n\n  try {\n    // 1. 获取微信登录code\n    const code = await getWechatLoginCode();\n\n    // 2. 直接执行登录，使用固定的默认值\n    const result = await performWechatLoginWithUserInfo({\n      code,\n      nickname: '微信用户',  // 固定昵称\n      avatarUrl: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0',  // 固定头像\n      showLoading: false,  // 我们自己管理加载状态\n      autoNavigate: true   // 自动处理导航\n    });\n\n    if (result.success) {\n      console.log('登录成功:', result.type);\n    } else {\n      showLoginError(result.error);\n    }\n  } catch (error) {\n    console.error('登录失败:', error);\n    showLoginError(error.message || '登录失败');\n  } finally {\n    isLogging.value = false;\n  }\n}\n\n// 获取微信登录code\nfunction getWechatLoginCode() {\n  return new Promise((resolve, reject) => {\n    wx.login({\n      success: (res) => {\n        if (res.code) {\n          resolve(res.code);\n        } else {\n          reject(new Error('获取微信登录code失败'));\n        }\n      },\n      fail: (error) => {\n        reject(new Error('微信登录失败：' + error.errMsg));\n      }\n    });\n  });\n}\n\n// 注意：showNicknameInputModal 函数已移除\n// 现在使用固定的默认昵称，无需用户输入\n\n// 执行带用户信息的微信登录\nasync function performWechatLoginWithUserInfo({ code, nickname, avatarUrl, showLoading = true, autoNavigate = true }) {\n  try {\n    if (showLoading) {\n      uni.showLoading({\n        title: '登录中...',\n        mask: true\n      });\n    }\n\n    // 调用后端登录接口\n    const response = await api.wechat.login({\n      js_code: code,\n      nickname: nickname,\n      avatar_url: avatarUrl\n    });\n\n    if (response && response.success !== false) {\n      // 保存用户信息到本地\n      const userInfo = {\n        nickName: nickname,\n        avatarUrl: avatarUrl\n      };\n\n      // 保存到微信认证工具\n      wechatAuth.handleNicknameInput(nickname);\n      userStore.setWechatUserInfo(userInfo);\n\n      console.log(response.data,'===userInfo.is_staff',response.data.user_info)\n      // 数据映射：将接口返回的特定字段赋值给页面变量\n\t\t\t/* if (response.user_info.wechat_avatar_url) {\n\t\t\t\twechatAvatarUrl.value = response.user_info.wechat_avatar_url;\n\t\t\t}\n\t\t\tif (response.user_info.wechat_nickname) {\n\t\t\t\twechatNickname.value = response.user_info.wechat_nickname;\n\t\t\t} */\n\t\t\tif (response.data.wechat_openid) {\n\t\t\t\t// wechatOpenid.value = response.data.wechat_openid;\n\t\t\t\t// 存储到本地存储\n\t\t\t\tuni.setStorageSync('wechat_openid', response.data.wechat_openid);\n\t\t\t}\n\t\t\t// 映射认证状态字段\n\t\t\tif (response.data.user_info.is_staff) {\n\t\t\t\t// isStaff.value = response.user_info.is_staff;\n\t\t\t\t// 存储认证状态到本地\n\t\t\t\tuni.setStorageSync('user_is_staff', response.data.user_info.is_staff);\n\t\t\t}\n\n      uni.showToast({\n        title: '登录成功',\n        icon: 'success',\n        duration: 2000\n      });\n\n      // 自动导航\n      if (autoNavigate) {\n        setTimeout(() => {\n          uni.switchTab({\n            url: '/pages/mine/mine'\n          });\n        }, 2000);\n      }\n\n      return {\n        success: true,\n        type: LOGIN_RESULT_TYPE.SUCCESS,\n        userInfo: response.data.user_info,\n        wechatUserInfo: userInfo\n      };\n    } else {\n      throw new Error('登录失败');\n    }\n  } catch (error) {\n    console.error('微信登录失败:', error);\n    return {\n      success: false,\n      error: error.message || '登录失败'\n    };\n  } finally {\n    if (showLoading) {\n      uni.hideLoading();\n    }\n  }\n}\n\n// 发送验证码\n/* async function sendVerifyCode() {\n  if (!canSendCode.value) return;\n  \n  try {\n    // 调用发送验证码API\n    await api.user.sendVerifyCode({\n      phone: phoneForm.value.phone\n    });\n    \n    // 开始倒计时\n    countdown.value = 60;\n    const timer = setInterval(() => {\n      countdown.value--;\n      if (countdown.value <= 0) {\n        clearInterval(timer);\n      }\n    }, 1000);\n    \n    uni.showToast({\n      title: '验证码已发送',\n      icon: 'success'\n    });\n  } catch (error) {\n    uni.showToast({\n      title: error.message || '发送验证码失败',\n      icon: 'none'\n    });\n  }\n} */\n\n// 手机号登录\n/* async function handlePhoneLogin() {\n  if (!canSubmitPhone.value) return;\n  \n  try {\n    isPhoneLogging.value = true;\n    \n    const result = await api.user.phoneLogin({\n      phone: phoneForm.value.phone,\n      code: phoneForm.value.code\n    });\n    \n    if (result.success) {\n      // 保存登录信息\n      api.auth.setToken(result.data.token);\n      userStore.setUserInfo(result.data.userInfo);\n      \n      uni.showToast({\n        title: '登录成功',\n        icon: 'success'\n      });\n      \n      closePhoneLogin();\n      \n      setTimeout(() => {\n        uni.switchTab({\n          url: '/pages/index/index'\n        });\n      }, 1500);\n    }\n  } catch (error) {\n    uni.showToast({\n      title: error.message || '登录失败',\n      icon: 'none'\n    });\n  } finally {\n    isPhoneLogging.value = false;\n  }\n} */\n\n// 关闭手机登录弹窗\nfunction closePhoneLogin() {\n  showPhoneLogin.value = false;\n  // 重置表单\n  phoneForm.value = {\n    phone: '',\n    code: ''\n  };\n  countdown.value = 0;\n}\n\n// 显示隐私政策\nfunction showPrivacyPolicy() {\n  navigate.toPrivacyPolicy();\n}\n\n// 显示用户协议\nfunction showUserAgreement() {\n  navigate.toUserAgreement();\n}\n\n// 监听弹窗显示\nfunction onPhoneLoginShow() {\n  // 手机登录弹窗功能已移除，使用微信登录\n  console.log('手机登录功能已移除，请使用微信登录');\n}\n\n// 监听showPhoneLogin变化\nwatch(() => showPhoneLogin.value, (newVal) => {\n  if (newVal) {\n    onPhoneLoginShow();\n  }\n});\n</script>\n\n<style lang=\"scss\" scoped>\n.login-container {\n  min-height: 100vh;\n  background: transparent;\n  display: flex;\n  flex-direction: column;\n  padding: 60rpx 40rpx 40rpx;\n}\n\n.logo-section {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  margin-top: 100rpx;\n}\n\n.logo {\n  width: 160rpx;\n  height: 160rpx;\n  margin-bottom: 40rpx;\n}\n\n.app-name {\n  font-size: 48rpx;\n  font-weight: bold;\n  color: #080808;\n  margin-bottom: 20rpx;\n}\n\n.app-desc {\n  font-size: 28rpx;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.login-form {\n  margin-top: 120rpx;\n}\n\n.wechat-login-btn {\n  width: 100%;\n  height: 100rpx;\n  background-color: #07c160;\n  color: #ffffff;\n  border-radius: 50rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 32rpx;\n  font-weight: 500;\n  margin-bottom: 40rpx;\n  border: none;\n}\n\n.wechat-login-btn:disabled {\n  background-color: #cccccc;\n}\n\n.wechat-icon {\n  font-family: 'iconfont';\n  font-size: 36rpx;\n  margin-right: 20rpx;\n}\n\n.other-login {\n  margin-top: 60rpx;\n}\n\n.divider {\n  text-align: center;\n  margin-bottom: 40rpx;\n}\n\n.divider-text {\n  font-size: 24rpx;\n  color: #999999;\n  background: transparent;\n  padding: 0 20rpx;\n}\n\n.phone-login-btn {\n  width: 100%;\n  height: 88rpx;\n  background-color: #f8f8f8;\n  color: #333333;\n  border: 2rpx solid #e5e5e5;\n  border-radius: 44rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 30rpx;\n}\n\n.phone-icon {\n  font-family: 'iconfont';\n  font-size: 32rpx;\n  margin-right: 16rpx;\n}\n\n.agreement-section {\n  text-align: center;\n}\n\n.agreement-text {\n  display: flex;\n  flex-wrap: wrap;\n  font-size: 30rpx;\n  color: #989898;\n}\n\n.link {\n  color: #6E9FEB;\n}\n\n\n.phone-login-popup {\n  background-color: #ffffff;\n  border-radius: 20rpx 20rpx 0 0;\n  padding: 40rpx;\n}\n\n.popup-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 40rpx;\n}\n\n.popup-title {\n  font-size: 36rpx;\n  font-weight: 500;\n  color: #333333;\n}\n\n.popup-close {\n  font-size: 48rpx;\n  color: #999999;\n}\n\n.phone-form {\n  .input-group {\n    margin-bottom: 30rpx;\n    display: flex;\n    align-items: center;\n  }\n  \n  .phone-input,\n  .code-input {\n    flex: 1;\n    height: 88rpx;\n    border: 2rpx solid #e5e5e5;\n    border-radius: 8rpx;\n    padding: 0 20rpx;\n    font-size: 30rpx;\n  }\n  \n  .send-code-btn {\n    width: 200rpx;\n    height: 88rpx;\n    background-color: #007aff;\n    color: #ffffff;\n    border-radius: 8rpx;\n    font-size: 26rpx;\n    margin-left: 20rpx;\n  }\n  \n  .send-code-btn:disabled {\n    background-color: #cccccc;\n  }\n  \n  .phone-submit-btn {\n    width: 100%;\n    height: 88rpx;\n    background-color: #007aff;\n    color: #ffffff;\n    border-radius: 8rpx;\n    font-size: 32rpx;\n    margin-top: 20rpx;\n  }\n  \n  .phone-submit-btn:disabled {\n    background-color: #cccccc;\n  }\n}\n\n.loading-mask {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 9999;\n}\n\n.loading-content {\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  padding: 40rpx;\n}\n</style>\n", "import MiniProgramPage from 'D:/work/不良资产系统/non-performing-assets/pages/login/login.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onMounted", "checkAndHandleLoginStatus", "uni", "showLoginE<PERSON>r", "wx", "api", "wechatAuth", "userStore", "LOGIN_RESULT_TYPE", "navigate", "watch"], "mappings": ";;;;;;;;;;;;;;;;;;;AAuEA,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAC3B,UAAM,iBAAiBA,cAAAA,IAAI,KAAK;AAChC,UAAM,iBAAiBA,cAAAA,IAAI,KAAK;AAChC,UAAM,YAAYA,cAAAA,IAAI,CAAC;AAGvB,UAAM,YAAYA,cAAAA,IAAI;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAGD,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB,aAAa;AAAA,QACX,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,eAAe;AAAA,MAChB;AAAA,IACH,CAAC;AAED,UAAM,WAAWA,cAAAA,IAAI,KAAK;AAE1B,UAAM,kBAAkB,MAAM;AAC5B,eAAS,QAAQ,CAAC,SAAS;AAAA,IAC7B;AAGoBC,kBAAAA,SAAS,MAAM;AACjC,aAAO,UAAU,MAAM,MAAM,WAAW,MAAM,UAAU,UAAU;AAAA,IACpE,CAAC;AAEsBA,kBAAAA,SAAS,MAAM;AACpC,aAAO,UAAU,MAAM,MAAM,WAAW,MACjC,UAAU,MAAM,KAAK,WAAW,KAChC,CAAC,eAAe;AAAA,IACzB,CAAC;AAEsBA,kBAAAA,SAAS,MAAM;AACpC,aAAO,UAAU,QAAQ,IAAI,GAAG,UAAU,KAAK,MAAM;AAAA,IACvD,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AAEd;IACF,CAAC;AAGD,mBAAe,mBAAmB;AAChC,UAAI;AACF,cAAM,SAAS,MAAMC,4CAA0B;AAAA,UAC7C,WAAW;AAAA;AAAA,UACX,cAAc;AAAA;AAAA,UACd,aAAa;AAAA;AAAA,QACnB,CAAK;AAED,YAAI,OAAO,WAAW,OAAO,SAAS;AAEpCC,wBAAAA,MAAA,MAAA,OAAA,gCAAY,YAAY;AAAA,QACzB;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,gCAAc,aAAa,KAAK;AAAA,MACjC;AAAA,IACH;AAGA,mBAAe,oBAAoB;AACjC,UAAI,UAAU;AAAO;AAErB,UAAI,CAAC,SAAS,OAAO;AACnBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,gBAAU,QAAQ;AAElB,UAAI;AAEF,cAAM,OAAO,MAAM;AAGnB,cAAM,SAAS,MAAM,+BAA+B;AAAA,UAClD;AAAA,UACA,UAAU;AAAA;AAAA,UACV,WAAW;AAAA;AAAA,UACX,aAAa;AAAA;AAAA,UACb,cAAc;AAAA;AAAA,QACpB,CAAK;AAED,YAAI,OAAO,SAAS;AAClBA,wBAAY,MAAA,MAAA,OAAA,gCAAA,SAAS,OAAO,IAAI;AAAA,QACtC,OAAW;AACLC,2CAAe,OAAO,KAAK;AAAA,QAC5B;AAAA,MACF,SAAQ,OAAO;AACdD,2EAAc,SAAS,KAAK;AAC5BC,0BAAAA,eAAe,MAAM,WAAW,MAAM;AAAA,MAC1C,UAAY;AACR,kBAAU,QAAQ;AAAA,MACnB;AAAA,IACH;AAGA,aAAS,qBAAqB;AAC5B,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCC,sBAAAA,KAAG,MAAM;AAAA,UACP,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,MAAM;AACZ,sBAAQ,IAAI,IAAI;AAAA,YAC1B,OAAe;AACL,qBAAO,IAAI,MAAM,cAAc,CAAC;AAAA,YACjC;AAAA,UACF;AAAA,UACD,MAAM,CAAC,UAAU;AACf,mBAAO,IAAI,MAAM,YAAY,MAAM,MAAM,CAAC;AAAA,UAC3C;AAAA,QACP,CAAK;AAAA,MACL,CAAG;AAAA,IACH;AAMA,mBAAe,+BAA+B,EAAE,MAAM,UAAU,WAAW,cAAc,MAAM,eAAe,QAAQ;AACpH,UAAI;AACF,YAAI,aAAa;AACfF,wBAAAA,MAAI,YAAY;AAAA,YACd,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAGD,cAAM,WAAW,MAAMG,cAAI,OAAO,MAAM;AAAA,UACtC,SAAS;AAAA,UACT;AAAA,UACA,YAAY;AAAA,QAClB,CAAK;AAED,YAAI,YAAY,SAAS,YAAY,OAAO;AAE1C,gBAAM,WAAW;AAAA,YACf,UAAU;AAAA,YACV;AAAA,UACR;AAGMC,sCAAW,oBAAoB,QAAQ;AACvCC,oCAAU,kBAAkB,QAAQ;AAEpCL,8BAAA,MAAA,OAAA,gCAAY,SAAS,MAAK,wBAAuB,SAAS,KAAK,SAAS;AAQ3E,cAAI,SAAS,KAAK,eAAe;AAGhCA,0BAAG,MAAC,eAAe,iBAAiB,SAAS,KAAK,aAAa;AAAA,UAC/D;AAED,cAAI,SAAS,KAAK,UAAU,UAAU;AAGrCA,0BAAG,MAAC,eAAe,iBAAiB,SAAS,KAAK,UAAU,QAAQ;AAAA,UACpE;AAEEA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAGD,cAAI,cAAc;AAChB,uBAAW,MAAM;AACfA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,KAAK;AAAA,cACjB,CAAW;AAAA,YACF,GAAE,GAAI;AAAA,UACR;AAED,iBAAO;AAAA,YACL,SAAS;AAAA,YACT,MAAMM,kBAAiB,kBAAC;AAAA,YACxB,UAAU,SAAS,KAAK;AAAA,YACxB,gBAAgB;AAAA,UACxB;AAAA,QACA,OAAW;AACL,gBAAM,IAAI,MAAM,MAAM;AAAA,QACvB;AAAA,MACF,SAAQ,OAAO;AACdN,sBAAA,MAAA,MAAA,SAAA,gCAAc,WAAW,KAAK;AAC9B,eAAO;AAAA,UACL,SAAS;AAAA,UACT,OAAO,MAAM,WAAW;AAAA,QAC9B;AAAA,MACA,UAAY;AACR,YAAI,aAAa;AACfA,wBAAG,MAAC,YAAW;AAAA,QAChB;AAAA,MACF;AAAA,IACH;AAqFA,aAAS,oBAAoB;AAC3BO,oBAAQ,SAAC,gBAAe;AAAA,IAC1B;AAGA,aAAS,oBAAoB;AAC3BA,oBAAQ,SAAC,gBAAe;AAAA,IAC1B;AAGA,aAAS,mBAAmB;AAE1BP,oBAAAA,mDAAY,mBAAmB;AAAA,IACjC;AAGAQ,kBAAK,MAAC,MAAM,eAAe,OAAO,CAAC,WAAW;AAC5C,UAAI,QAAQ;AACV;MACD;AAAA,IACH,CAAC;;;;;;;;;;;;;;;;;;;;;;AChYD,GAAG,WAAW,eAAe;"}