{"version": 3, "file": "mine.js", "sources": ["pages/mine/mine.vue", "C:/software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbWluZS9taW5lLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"mine-container\">\r\n\t\t<!-- 用户信息区域 -->\r\n\t\t<view class=\"auth\">\r\n\t\t\t<!-- 加载状态骨架屏 -->\r\n\t\t\t<view v-if=\"isLoading\" class=\"loading-skeleton\">\r\n\t\t\t\t<view class=\"skeleton-user-info\">\r\n\t\t\t\t\t<view class=\"skeleton-avatar\"></view>\r\n\t\t\t\t\t<view class=\"skeleton-details\">\r\n\t\t\t\t\t\t<view class=\"skeleton-name\"></view>\r\n\t\t\t\t\t\t<view class=\"skeleton-tip\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"skeleton-button\"></view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 用户信息内容 -->\r\n\t\t\t<view v-else class=\"user-info-section\">\r\n\t\t\t\t<view class=\"avatar-container\">\r\n\t\t\t\t\t<image class=\"avatar\" :src=\"displayAvatar\" @error=\"handleAvatarError\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"user-details\">\r\n\t\t\t\t\t<view class=\"username\" @click=\"!isLoggedIn && navigateTo('/pages/login/login')\">\r\n\t\t\t\t\t\t{{ isLoggedIn ? displayName : '点击登录' }}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 认证状态显示 -->\r\n\t\t\t\t\t<view v-if=\"isLoggedIn\" class=\"auth-status\" :class=\"authStatus\">\r\n\t\t\t\t\t\t<text v-if=\"authStatus == 'authenticated'\" class=\"fas fa-check-circle\"></text>\r\n\t\t\t\t\t\t<text v-else class=\"fas fa-exclamation-circle\"></text>\r\n\t\t\t\t\t\t{{ authStatusText }}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- <view v-if=\"!isLoggedIn\" class=\"login-tip\">登录后享受更多服务</view> -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 操作按钮 - 根据认证状态显示 -->\r\n\t\t\t<!-- 已认证用户 -->\r\n\t\t\t<!-- <button\r\n\t\t\t\tclass=\"auth-button authenticated\"\r\n\t\t\t\t@click=\"handleAuthenticatedAction\"\r\n\t\t\t\tv-if=\"isLoggedIn && authStatus === 'authenticated' && !isLoading\"\r\n\t\t\t\t:disabled=\"isLoading\"\r\n\t\t\t>\r\n\t\t\t\t<text class=\"fas fa-check\"></text>\r\n\t\t\t\t认证完成 - 查看更多功能\r\n\t\t\t</button> -->\r\n\t\t\t\r\n\t\t\t<!-- 未认证用户 -->\r\n\t\t\t<!-- <button\r\n\t\t\t\tclass=\"auth-button need-auth\"\r\n\t\t\t\t@click=\"handleAuth\"\r\n\t\t\t\tv-if=\"isLoggedIn && authStatus === 'need_auth' && !isLoading\"\r\n\t\t\t\t:disabled=\"isLoading\"\r\n\t\t\t>\r\n\t\t\t\t<text class=\"fas fa-user-check\"></text>\r\n\t\t\t\t实名认证\r\n\t\t\t</button> -->\r\n\t\t\t\r\n\t\t\t<!-- 未登录用户 -->\r\n\t\t\t<button\r\n\t\t\t\tclass=\"login-button\"\r\n\t\t\t\t@click=\"navigateTo('/pages/login/login')\"\r\n\t\t\t\tv-if=\"!isLoggedIn && !isLoading\"\r\n\t\t\t\t:disabled=\"isLoading\"\r\n\t\t\t>\r\n\t\t\t\t立即登录\r\n\t\t\t</button>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 功能列表 - 根据认证状态显示 -->\r\n\t\t<view class=\"function-list\">\r\n\t\t\t<!-- 已认证用户可访问的功能 -->\r\n\t\t\t<view \r\n\t\t\t\tv-if=\"isLoggedIn || authStatus === 'authenticated'\"\r\n\t\t\t\tclass=\"function-item\"\r\n\t\t\t\t@click=\"navigateTo('/pages/mediation_query/mediation_query')\"\r\n\t\t\t>\r\n\t\t\t\t<view class=\"function-item-left\">\r\n\t\t\t\t\t<text class=\"fas fa-clipboard-list\"></text>\r\n\t\t\t\t\t<text class=\"function-text\">我的调解记录</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text class=\"fas fa-chevron-right\"></text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 未认证用户的功能提示 -->\r\n\t\t\t<!-- <view \r\n\t\t\t\tv-if=\"authStatus === 'need_auth'\"\r\n\t\t\t\tclass=\"function-item disabled\"\r\n\t\t\t\t@click=\"showAuthTip\"\r\n\t\t\t>\r\n\t\t\t\t<view class=\"function-item-left\">\r\n\t\t\t\t\t<text class=\"fas fa-clipboard-list\"></text>\r\n\t\t\t\t\t<text class=\"function-text\">我的调解记录</text>\r\n\t\t\t\t\t<text class=\"auth-required-tag\">需认证</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text class=\"fas fa-lock\"></text>\r\n\t\t\t</view> -->\r\n\t\t\t\r\n\t\t\t<!-- 通用功能 - 所有用户都可访问 -->\r\n\t\t\t<view class=\"function-item\" @click=\"navigateTo('/pages/privacy_policy/privacy_policy')\">\r\n\t\t\t\t<view class=\"function-item-left\">\r\n\t\t\t\t\t<text class=\"fas fa-shield-alt\"></text>\r\n\t\t\t\t\t<text class=\"function-text\">隐私政策</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text class=\"fas fa-chevron-right\"></text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\r\n\t\t\r\n\t\t<!-- <button\r\n\t\t\tclass=\"btn-danger\"\r\n\t\t\t@click=\"handleLogout\"\r\n\t\t\tv-if=\"isLoggedIn\"\r\n\t\t>\r\n\t\t\t退出登录\r\n\t\t</button> -->\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted } from 'vue';\r\nimport userStore, { userComputed } from '@/utils/user-store.js';\r\nimport { api } from '@/utils/api.js';\r\n\r\n// 响应式数据\r\nconst isLoading = ref(false);\r\nconst wechatAvatarUrl = ref('');\r\nconst wechatNickname = ref('');\r\nconst wechatOpenid = ref('');\r\nconst detectAuthResult = ref(false); // 用户认证状态：true=已认证，false=未认证\r\n\r\n// 计算属性 - 使用用户状态管理\r\nconst userInfo = computed(() => userStore.getUserState());\r\nconst isLoggedIn = userComputed.isLoggedIn;\r\nconst displayName = computed(() => {\r\n\treturn wechatNickname.value || userComputed.displayName.value || userComputed.wechatUserInfo.nickName || '用户';\r\n});\r\nconst displayAvatar = computed(() => {\r\n\treturn wechatAvatarUrl.value || userComputed.displayAvatar.value || userComputed.wechatUserInfo.avatarUrl || '/static/default-avatar.png';\r\n});\r\nconst isVerified = userComputed.isVerified;\r\n\r\n// 认证状态计算属性\r\nconst authStatus = computed(() => {\r\n\t// if (!isLoggedIn.value) return 'not_logged_in';\r\n\treturn uni.getStorageSync('detect_auth_result') || detectAuthResult.value ? 'authenticated' : 'need_auth';\r\n});\r\n\r\nconst authStatusText = computed(() => {\r\n\tswitch (authStatus.value) {\r\n\t\tcase 'authenticated':\r\n\t\t\treturn '已完成实名认证';\r\n\t\tcase 'need_auth':\r\n\t\t\treturn '请完成实名认证以使用完整功能';\r\n\t}\r\n});\r\n\r\n// 页面加载时执行\r\nonMounted(() => {\r\n\t// 从本地存储恢复数据\r\n\tconst storedOpenid = uni.getStorageSync('wechat_openid');\r\n\tif (storedOpenid) {\r\n\t\twechatOpenid.value = storedOpenid;\r\n\t}\r\n\t\r\n\tconst storedIsStaff = uni.getStorageSync('detect_auth_result');\r\n\tif (typeof storedIsStaff !== 'undefined') {\r\n\t\tdetectAuthResult.value = storedIsStaff;\r\n\t}\r\n\t\r\n\t// 检查登录状态并获取用户信息\r\n\tcheckLoginAndGetUserInfo();\r\n});\r\n\r\n// 检查登录状态并获取用户信息\r\nasync function checkLoginAndGetUserInfo() {\r\n\tif (!isLoggedIn.value) {\r\n\t\t// 未登录状态，清除加载状态\r\n\t\tisLoading.value = false;\r\n\t\treturn;\r\n\t}\r\n\r\n\ttry {\r\n\t\tisLoading.value = true;\r\n\t\t\r\n\t\t// 调用 /user/user_info/ 接口获取最新用户信息\r\n\t\tconst result = await api.user.getUserInfo();\r\n\t\t\r\n\t\tif (result.success && result.data) {\r\n\t\t\t// 数据映射：将接口返回的特定字段赋值给页面变量\r\n\t\t\tif (result.data.wechat_avatar_url) {\r\n\t\t\t\twechatAvatarUrl.value = result.data.wechat_avatar_url;\r\n\t\t\t}\r\n\t\t\tif (result.data.wechat_nickname) {\r\n\t\t\t\twechatNickname.value = result.data.wechat_nickname;\r\n\t\t\t}\r\n\t\t\tif (result.data.wechat_openid) {\r\n\t\t\t\twechatOpenid.value = result.data.wechat_openid;\r\n\t\t\t\t// 存储到本地存储\r\n\t\t\t\tuni.setStorageSync('wechat_openid', result.data.wechat_openid);\r\n\t\t\t}\r\n\t\t\t// 映射认证状态字段\r\n\t\t\tif (typeof result.data.detect_auth_result !== 'undefined') {\r\n\t\t\t\tdetectAuthResult.value = result.data.detect_auth_result;\r\n\t\t\t\t// 存储认证状态到本地\r\n\t\t\t\tuni.setStorageSync('detect_auth_result', result.data.detect_auth_result);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 更新用户信息到状态管理\r\n\t\t\tuserStore.updateUserInfo(result.data);\r\n\t\t\t\r\n\t\t\t// 记录操作日志\r\n\t\t\trecordOperationLog('查看', '个人中心');\r\n\t\t} else {\r\n\t\t\tconsole.warn('获取用户信息失败:', result.message || '未知错误');\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error('获取用户信息失败:', error);\r\n\t\t// 如果是401错误，说明token过期，跳转到登录页\r\n\t\tif (error.message && error.message.includes('401')) {\r\n\t\t\tuserStore.clearUserInfo();\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '登录过期',\r\n\t\t\t\tcontent: '您的登录已过期，请重新登录',\r\n\t\t\t\tshowCancel: false,\r\n\t\t\t\tsuccess: () => {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\t} finally {\r\n\t\tisLoading.value = false;\r\n\t}\r\n}\r\n\r\n\r\n\r\n// 处理头像加载错误\r\nfunction handleAvatarError() {\r\n\t// 使用默认头像\r\n\tconsole.log('头像加载失败，使用默认头像');\r\n}\r\n\r\n// 记录用户操作日志\r\nasync function recordOperationLog(action, target) {\r\n\ttry {\r\n\t\tif (!isLoggedIn.value) return;\r\n\t\t\r\n\t\tawait api.operationLog.recordOperation({\r\n\t\t\tbutton_name: action,       // 按钮名称\r\n\t\t\tbutton_type: target,       // 按钮类型\r\n\t\t\tpage_url: getCurrentPages()[getCurrentPages().length - 1].route, // 浏览器路径\r\n\t\t\tpage_plate: '个人中心',           // 菜单名称\r\n\t\t});\r\n\t} catch (error) {\r\n\t\tconsole.error('记录操作日志失败:', error);\r\n\t\t// 日志记录失败不影响正常流程\r\n\t}\r\n}\r\n\r\n// 身份认证处理\r\n/* function handleAuth() {\r\n\tif (!isLoggedIn.value) {\r\n\t\tuni.navigateTo({\r\n\t\t\turl: '/pages/login/login'\r\n\t\t});\r\n\t\treturn;\r\n\t}\r\n\r\n\t// 记录操作日志\r\n\trecordOperationLog('点击', '身份认证按钮');\r\n\t\r\n\t// 跳转到身份认证页面\r\n\tuni.navigateTo({\r\n\t\turl: '/pages/auth/auth'\r\n\t});\r\n} */\r\n\r\n// 已认证用户的操作处理\r\nfunction handleAuthenticatedAction() {\r\n\t// 记录操作日志\r\n\trecordOperationLog('点击', '已认证功能按钮');\r\n\t\r\n\tuni.showModal({\r\n\t\ttitle: '认证完成',\r\n\t\tcontent: '您已通过身份认证，可以使用完整功能。是否查看更多功能？',\r\n\t\tsuccess: (res) => {\r\n\t\t\tif (res.confirm) {\r\n\t\t\t\t// 可以跳转到功能列表或其他页面\r\n\t\t\t\tnavigateTo('/pages/mediation_query/mediation_query');\r\n\t\t\t}\r\n\t\t}\r\n\t});\r\n}\r\n\r\n// 显示认证提示\r\nfunction showAuthTip() {\r\n\tuni.showModal({\r\n\t\ttitle: '需要身份认证',\r\n\t\tcontent: '此功能需要先进行身份认证，是否立即前往认证？',\r\n\t\tsuccess: (res) => {\r\n\t\t\tif (res.confirm) {\r\n\t\t\t\thandleAuth();\r\n\t\t\t}\r\n\t\t}\r\n\t});\r\n}\r\n\r\n// 退出登录\r\n/* async function handleLogout() {\r\n\tuni.showModal({\r\n\t\ttitle: '确认退出',\r\n\t\tcontent: '确定要退出登录吗？',\r\n\t\tsuccess: async (res) => {\r\n\t\t\tif (res.confirm) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tawait userStore.logout();\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('退出登录失败:', error);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t});\r\n} */\r\n\r\n// 页面导航\r\nfunction navigateTo(url) {\r\n\tif (!isLoggedIn.value && url !== '/pages/login/login') {\r\n\t\tuni.navigateTo({\r\n\t\t\turl: '/pages/login/login'\r\n\t\t});\r\n\t\treturn;\r\n\t}\r\n\r\n\t// 记录操作日志\r\n\tconst pageName = getPageNameFromUrl(url);\r\n\trecordOperationLog('点击', pageName);\r\n\r\n\tuni.navigateTo({\r\n\t\turl: url\r\n\t});\r\n}\r\n\r\n// 从URL获取页面名称\r\nfunction getPageNameFromUrl(url) {\r\n\tconst pageMap = {\r\n\t\t'/pages/mediation_query/mediation_query': '我的调解记录',\r\n\t\t'/pages/privacy_policy/privacy_policy': '隐私政策',\r\n\t\t'/pages/login/login': '登录页面'\r\n\t};\r\n\treturn pageMap[url] || '未知页面';\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t:root{\r\n\t\t--primary-color:#3B7EEB;\r\n\t}\r\n\t.mine-container {\r\n\t\tmin-height: 100vh;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tpadding: 15px 15px 70px;\r\n\t}\r\n\t.auth{\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 10rpx;\r\n\t\tpadding: 40rpx 30rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\t.user-info-section {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding-bottom: 40rpx;\r\n\t}\r\n\t\r\n\t.avatar-container {\r\n\t\tmargin-right: 30rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t}\r\n\t\r\n\t.avatar {\r\n\t\twidth: 120rpx;\r\n\t\theight: 120rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tbackground-color: #f0f0f0;\r\n\t}\r\n\t\r\n\t.user-details {\r\n\t\tflex: 1;\r\n\t}\r\n\t\r\n\t.username {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\t\r\n\t/* 认证状态样式 */\r\n\t.auth-status {\r\n\t\tdisplay: inline-block;\r\n\t\tpadding: 2px 0;\r\n\t\tborder-radius: 4px;\r\n\t\tfont-size: 12px;\r\n\t\tmargin-top: 5px;\r\n\t\t\r\n\t\t&.authenticated {\r\n\t\t\tbackground-color: #E6F7FF;\r\n\t\t\tcolor: var(--primary-color);\r\n\t\t\t\r\n\t\t\t.fa-check-circle {\r\n\t\t\t\tcolor: #3B7EEB;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t&.need_auth {\r\n\t\t\t// background-color: #fff7e6;\r\n\t\t\tcolor: #979292;\r\n\t\t\t\r\n\t\t\t.fa-exclamation-circle {\r\n\t\t\t\tcolor: #979292;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.auth-button {\r\n\t\twidth: 640rpx;\r\n\t\theight: 80rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 32rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\t\r\n\t\t&.authenticated {\r\n\t\t\tbackground-color: #52c41a;\r\n\t\t\t\r\n\t\t\t.fas {\r\n\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t&.need-auth {\r\n\t\t\tbackground-color: #3B7EEB;\r\n\t\t\t\r\n\t\t\t.fas {\r\n\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.auth-button[disabled] {\r\n\t\tbackground-color: #cccccc;\r\n\t}\r\n\r\n\t.login-button {\r\n\t\twidth: 640rpx;\r\n\t\theight: 80rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tbackground-color: #07c160;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 32rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t}\r\n\r\n\t.auth-tip {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #ff6b35;\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\r\n\t.login-tip {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\t\r\n\t.function-item {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tbackground-color: white;\r\n\t\tborder-radius: 24rpx;\r\n\t\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\ttransition: var(--transition-normal);\r\n\t\tborder: 2rpx solid rgba(0, 0, 0, 0.03);\r\n\t\t\r\n\t\t&.disabled {\r\n\t\t\topacity: 0.6;\r\n\t\t\tbackground-color: #f5f5f5;\r\n\t\t\t\r\n\t\t\t.function-text {\r\n\t\t\t\tcolor: #999;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.fa-clipboard-list {\r\n\t\t\t\tcolor: #ccc;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.fa-lock {\r\n\t\t\t\tcolor: #fa8c16;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.function-item-left {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tposition: relative;\r\n\t}\r\n\t\r\n\t.auth-required-tag {\r\n\t\tbackground-color: #fa8c16;\r\n\t\tcolor: white;\r\n\t\tfont-size: 20rpx;\r\n\t\tpadding: 2rpx 8rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tmargin-left: 15rpx;\r\n\t}\r\n\t\r\n\t.function-text {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #333;\r\n\t\tmargin-left: 20rpx;\r\n\t}\r\n\t\r\n\t.fa-clipboard-list,.fa-shield-alt {\r\n\t\tfont-size: 40rpx;\r\n\t\tcolor: #3b7eeb;\r\n\t}\r\n\t\r\n\t.fa-chevron-right {\r\n\t\tcolor: #999;\r\n\t}\r\n\t.btn-danger {\r\n\t\tbackground-color: #f5222d;\r\n\t\tcolor: white;\r\n\t\tmargin-top: 20rpx;\r\n\t\tdisplay: block;\r\n\t\twidth: 100%;\r\n\t}\r\n\t\r\n\t/* 加载骨架屏样式 */\r\n\t.loading-skeleton {\r\n\t\t.skeleton-user-info {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding-bottom: 40rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.skeleton-avatar {\r\n\t\t\twidth: 120rpx;\r\n\t\t\theight: 120rpx;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tbackground: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\r\n\t\t\tbackground-size: 200% 100%;\r\n\t\t\tanimation: skeleton-loading 1.5s infinite;\r\n\t\t\tmargin-right: 30rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.skeleton-details {\r\n\t\t\tflex: 1;\r\n\t\t}\r\n\t\t\r\n\t\t.skeleton-name {\r\n\t\t\twidth: 200rpx;\r\n\t\t\theight: 36rpx;\r\n\t\t\tbackground: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\r\n\t\t\tbackground-size: 200% 100%;\r\n\t\t\tanimation: skeleton-loading 1.5s infinite;\r\n\t\t\tborder-radius: 4rpx;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.skeleton-tip {\r\n\t\t\twidth: 300rpx;\r\n\t\t\theight: 24rpx;\r\n\t\t\tbackground: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\r\n\t\t\tbackground-size: 200% 100%;\r\n\t\t\tanimation: skeleton-loading 1.5s infinite;\r\n\t\t\tborder-radius: 4rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.skeleton-button {\r\n\t\t\twidth: 640rpx;\r\n\t\t\theight: 80rpx;\r\n\t\t\tbackground: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\r\n\t\t\tbackground-size: 200% 100%;\r\n\t\t\tanimation: skeleton-loading 1.5s infinite;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t}\r\n\t}\r\n\t\r\n\t@keyframes skeleton-loading {\r\n\t\t0% {\r\n\t\t\tbackground-position: -200% 0;\r\n\t\t}\r\n\t\t100% {\r\n\t\t\tbackground-position: 200% 0;\r\n\t\t}\r\n\t}\r\n\t\r\n\r\n</style> ", "import MiniProgramPage from 'D:/work/不良资产系统/non-performing-assets/pages/mine/mine.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "userStore", "userComputed", "uni", "onMounted", "api"], "mappings": ";;;;;;;AAgIA,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAC3B,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,iBAAiBA,cAAAA,IAAI,EAAE;AAC7B,UAAM,eAAeA,cAAAA,IAAI,EAAE;AAC3B,UAAM,mBAAmBA,cAAAA,IAAI,KAAK;AAGjBC,kBAAAA,SAAS,MAAMC,gBAAAA,UAAU,cAAc;AACxD,UAAM,aAAaC,gBAAY,aAAC;AAChC,UAAM,cAAcF,cAAQ,SAAC,MAAM;AAClC,aAAO,eAAe,SAASE,gBAAAA,aAAa,YAAY,SAASA,6BAAa,eAAe,YAAY;AAAA,IAC1G,CAAC;AACD,UAAM,gBAAgBF,cAAQ,SAAC,MAAM;AACpC,aAAO,gBAAgB,SAASE,gBAAAA,aAAa,cAAc,SAASA,6BAAa,eAAe,aAAa;AAAA,IAC9G,CAAC;AAID,UAAM,aAAaF,cAAQ,SAAC,MAAM;AAEjC,aAAOG,cAAAA,MAAI,eAAe,oBAAoB,KAAK,iBAAiB,QAAQ,kBAAkB;AAAA,IAC/F,CAAC;AAED,UAAM,iBAAiBH,cAAQ,SAAC,MAAM;AACrC,cAAQ,WAAW,OAAK;AAAA,QACvB,KAAK;AACJ,iBAAO;AAAA,QACR,KAAK;AACJ,iBAAO;AAAA,MACR;AAAA,IACF,CAAC;AAGDI,kBAAAA,UAAU,MAAM;AAEf,YAAM,eAAeD,cAAAA,MAAI,eAAe,eAAe;AACvD,UAAI,cAAc;AACjB,qBAAa,QAAQ;AAAA,MACrB;AAED,YAAM,gBAAgBA,cAAAA,MAAI,eAAe,oBAAoB;AAC7D,UAAI,OAAO,kBAAkB,aAAa;AACzC,yBAAiB,QAAQ;AAAA,MACzB;AAGD;IACD,CAAC;AAGD,mBAAe,2BAA2B;AACzC,UAAI,CAAC,WAAW,OAAO;AAEtB,kBAAU,QAAQ;AAClB;AAAA,MACA;AAED,UAAI;AACH,kBAAU,QAAQ;AAGlB,cAAM,SAAS,MAAME,UAAAA,IAAI,KAAK,YAAW;AAEzC,YAAI,OAAO,WAAW,OAAO,MAAM;AAElC,cAAI,OAAO,KAAK,mBAAmB;AAClC,4BAAgB,QAAQ,OAAO,KAAK;AAAA,UACpC;AACD,cAAI,OAAO,KAAK,iBAAiB;AAChC,2BAAe,QAAQ,OAAO,KAAK;AAAA,UACnC;AACD,cAAI,OAAO,KAAK,eAAe;AAC9B,yBAAa,QAAQ,OAAO,KAAK;AAEjCF,0BAAG,MAAC,eAAe,iBAAiB,OAAO,KAAK,aAAa;AAAA,UAC7D;AAED,cAAI,OAAO,OAAO,KAAK,uBAAuB,aAAa;AAC1D,6BAAiB,QAAQ,OAAO,KAAK;AAErCA,0BAAG,MAAC,eAAe,sBAAsB,OAAO,KAAK,kBAAkB;AAAA,UACvE;AAGDF,0BAAAA,UAAU,eAAe,OAAO,IAAI;AAGpC,6BAAmB,MAAM,MAAM;AAAA,QAClC,OAAS;AACNE,8BAAA,MAAA,QAAA,8BAAa,aAAa,OAAO,WAAW,MAAM;AAAA,QAClD;AAAA,MACD,SAAQ,OAAO;AACfA,yEAAc,aAAa,KAAK;AAEhC,YAAI,MAAM,WAAW,MAAM,QAAQ,SAAS,KAAK,GAAG;AACnDF,0BAAS,UAAC,cAAa;AACvBE,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,SAAS,MAAM;AACdA,4BAAAA,MAAI,WAAW;AAAA,gBACd,KAAK;AAAA,cACX,CAAM;AAAA,YACD;AAAA,UACL,CAAI;AAAA,QACD;AAAA,MACH,UAAW;AACT,kBAAU,QAAQ;AAAA,MAClB;AAAA,IACF;AAKA,aAAS,oBAAoB;AAE5BA,oBAAAA,iDAAY,eAAe;AAAA,IAC5B;AAGA,mBAAe,mBAAmB,QAAQ,QAAQ;AACjD,UAAI;AACH,YAAI,CAAC,WAAW;AAAO;AAEvB,cAAME,UAAG,IAAC,aAAa,gBAAgB;AAAA,UACtC,aAAa;AAAA;AAAA,UACb,aAAa;AAAA;AAAA,UACb,UAAU,gBAAe,EAAG,gBAAiB,EAAC,SAAS,CAAC,EAAE;AAAA;AAAA,UAC1D,YAAY;AAAA;AAAA,QACf,CAAG;AAAA,MACD,SAAQ,OAAO;AACfF,yEAAc,aAAa,KAAK;AAAA,MAEhC;AAAA,IACF;AAoEA,aAAS,WAAW,KAAK;AACxB,UAAI,CAAC,WAAW,SAAS,QAAQ,sBAAsB;AACtDA,sBAAAA,MAAI,WAAW;AAAA,UACd,KAAK;AAAA,QACR,CAAG;AACD;AAAA,MACA;AAGD,YAAM,WAAW,mBAAmB,GAAG;AACvC,yBAAmB,MAAM,QAAQ;AAEjCA,oBAAAA,MAAI,WAAW;AAAA,QACd;AAAA,MACF,CAAE;AAAA,IACF;AAGA,aAAS,mBAAmB,KAAK;AAChC,YAAM,UAAU;AAAA,QACf,0CAA0C;AAAA,QAC1C,wCAAwC;AAAA,QACxC,sBAAsB;AAAA,MACxB;AACC,aAAO,QAAQ,GAAG,KAAK;AAAA,IACxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnWA,GAAG,WAAW,eAAe;"}