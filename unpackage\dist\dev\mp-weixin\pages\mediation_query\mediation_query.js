"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const utils_api = require("../../utils/api.js");
const utils_userStore = require("../../utils/user-store.js");
if (!Array) {
  const _easycom_uni_easyinput2 = common_vendor.resolveComponent("uni-easyinput");
  _easycom_uni_easyinput2();
}
const _easycom_uni_easyinput = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-easyinput/uni-easyinput.js";
if (!Math) {
  _easycom_uni_easyinput();
}
const _sfc_main = {
  __name: "mediation_query",
  setup(__props) {
    const searchKeyword = common_vendor.ref("");
    const isRefreshing = common_vendor.ref(false);
    const isLoading = common_vendor.ref(false);
    const isLoadingAnimate = common_vendor.ref(false);
    const isAuthenticated = common_vendor.ref(false);
    const mediationCase = common_vendor.ref("");
    const orderList = common_vendor.ref([]);
    const formData = common_vendor.reactive({
      name: "",
      // 姓名
      card: ""
      // 身份证号
    });
    const statusStyles = {
      draft: { bgColor: "#faad14" },
      pending_confirm: { bgColor: "#faad14" },
      initiated: { bgColor: "#1890ff" },
      in_progress: { bgColor: "#1890ff" },
      completed: { bgColor: "#52c41a" },
      closed: { bgColor: "#999" }
    };
    const filteredOrderList = common_vendor.computed(() => {
      if (!searchKeyword.value) {
        return orderList.value;
      }
      const keyword = searchKeyword.value.toLowerCase();
      return orderList.value.filter(
        (order) => order.id.toLowerCase().includes(keyword)
      );
    });
    common_vendor.onMounted(() => {
      common_vendor.index.__f__("log", "at pages/mediation_query/mediation_query.vue:169", "调解查询页面已加载");
      checkAuthStatus();
      setTimeout(() => {
        isLoadingAnimate.value = true;
      }, 200);
    });
    const checkAuthStatus = async () => {
      const userIsStaff = common_vendor.index.getStorageSync("user_is_staff");
      if (userIsStaff === false) {
        common_vendor.index.__f__("log", "at pages/mediation_query/mediation_query.vue:182", "检测到 user_is_staff 为 false，正在获取最新用户信息...");
        const result = await utils_api.api.user.getUserInfo();
        if (result && result.success && result.data) {
          const userData = result.data;
          const updatedUserInfo = {
            ...userData,
            // 确保 isVerified 字段被正确更新
            isVerified: userData.isVerified || userData.is_verified || false
          };
          utils_userStore.userStore.updateUserInfo(updatedUserInfo);
          if (typeof userData.is_staff !== "undefined") {
            common_vendor.index.setStorageSync("user_is_staff", userData.is_staff);
            isAuthenticated.value = userData.is_staff === true;
          } else {
            isAuthenticated.value = userIsStaff === true;
          }
          common_vendor.index.__f__("log", "at pages/mediation_query/mediation_query.vue:208", "用户信息已更新:", updatedUserInfo);
          common_vendor.index.__f__("log", "at pages/mediation_query/mediation_query.vue:209", "用户认证状态:", isAuthenticated.value ? "已认证" : "未认证");
        } else {
          isAuthenticated.value = userIsStaff === true;
          common_vendor.index.__f__("log", "at pages/mediation_query/mediation_query.vue:213", "获取用户信息失败，使用缓存状态:", isAuthenticated.value ? "已认证" : "未认证");
        }
      } else {
        isAuthenticated.value = userIsStaff === true;
        common_vendor.index.__f__("log", "at pages/mediation_query/mediation_query.vue:218", "用户认证状态:", isAuthenticated.value ? "已认证" : "未认证");
      }
      if (isAuthenticated.value) {
        fetchOrderList();
      }
    };
    common_vendor.onPullDownRefresh(() => {
      common_vendor.index.__f__("log", "at pages/mediation_query/mediation_query.vue:229", "触发下拉刷新");
      isRefreshing.value = true;
      checkAuthStatus();
    });
    const fetchOrderList = async (caseNumber = "") => {
      isLoading.value = true;
      if (!isRefreshing.value) {
        common_vendor.index.showLoading({ title: "加载中..." });
      }
      try {
        const params = {};
        if (caseNumber) {
          params.mediation_case_number = caseNumber;
        }
        const result = await utils_api.api.mediationQuery.getAuthenticatedList(params);
        if (!isRefreshing.value) {
          common_vendor.index.hideLoading();
        }
        if (result && result.success && result.data) {
          orderList.value = result.data.map((item) => ({
            id: item.case_number || item.id,
            status: item.case_status_cn || "未知状态",
            statusCode: getStatusCode(item.case_status_cn),
            createDate: formatDate(item.initiate_date),
            rawData: item
          }));
        } else {
          orderList.value = [];
        }
        isLoading.value = false;
        if (isRefreshing.value) {
          common_vendor.index.stopPullDownRefresh();
          isRefreshing.value = false;
          common_vendor.index.showToast({
            title: "刷新成功",
            icon: "success",
            duration: 1500
          });
        }
      } catch (error) {
        if (!isRefreshing.value) {
          common_vendor.index.hideLoading();
        }
        common_vendor.index.__f__("error", "at pages/mediation_query/mediation_query.vue:283", "获取案件列表失败:", error);
        orderList.value = [];
        isLoading.value = false;
        if (isRefreshing.value) {
          common_vendor.index.stopPullDownRefresh();
          isRefreshing.value = false;
        }
        common_vendor.index.showToast({
          title: "获取数据失败",
          icon: "none",
          duration: 2e3
        });
      }
    };
    const handleSearch = async () => {
      if (isAuthenticated.value) {
        await fetchOrderList(searchKeyword.value.trim());
      } else {
        await handleIdentitySearch();
      }
    };
    const handleIdentitySearch = async () => {
      const name = formData.name.trim();
      const idCard = formData.card.trim();
      if (!name || !idCard) {
        common_vendor.index.showToast({
          title: "请输入完整的姓名和身份证号",
          icon: "none",
          duration: 1500
        });
        return;
      }
      common_vendor.index.showLoading({ title: "查询中..." });
      const result = await utils_api.api.mediationQuery.getCaseCountByIdentity({
        name,
        id_card: idCard
      });
      common_vendor.index.hideLoading();
      if (result.state == "success") {
        const count = result.data;
        mediationCase.value = count;
      } else {
        mediationCase.value = null;
        common_vendor.index.showToast({
          title: result.msg,
          icon: "none",
          duration: 2e3
        });
      }
    };
    const navigateToAuth = () => {
      const name = formData.name.trim();
      const idCard = formData.card.trim();
      common_vendor.index.navigateTo({
        url: `/pages/auth/auth?name=${encodeURIComponent(name)}&idCard=${encodeURIComponent(idCard)}`,
        success: () => {
          common_vendor.index.__f__("log", "at pages/mediation_query/mediation_query.vue:355", "跳转到认证页面");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/mediation_query/mediation_query.vue:358", "跳转失败", err);
          common_vendor.index.showToast({
            title: "跳转失败",
            icon: "none"
          });
        }
      });
    };
    const handleRefresh = () => {
      if (isAuthenticated.value) {
        fetchOrderList();
      }
    };
    const getStatusCode = (statusCn) => {
      const statusMap = {
        "待确认": "pending",
        "进行中": "processing",
        "已完成": "completed",
        "已关闭": "closed",
        "待处理": "pending",
        "处理中": "processing",
        "已结案": "completed",
        "已撤销": "closed"
      };
      return statusMap[statusCn] || "pending";
    };
    const formatDate = (dateStr) => {
      if (!dateStr)
        return "";
      const date = new Date(dateStr);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
    };
    const navigateToDetail = (orderId, orderStatus) => {
      common_vendor.index.__f__("log", "at pages/mediation_query/mediation_query.vue:398", orderId, "=====orderStatus", orderStatus);
      if (orderStatus === "待确认") {
        common_vendor.index.navigateTo({
          url: `/pages/work_order_detail/work_order_detail?id=${orderId}&status=${orderStatus}`
        });
      } else if (orderStatus === "进行中") {
        common_vendor.index.navigateTo({
          url: `/pages/solution_confirm/solution_confirm?orderId=${orderId}&status=${orderStatus}`
        });
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: !isAuthenticated.value
      }, !isAuthenticated.value ? common_vendor.e({
        b: common_vendor.o(($event) => formData.name = $event),
        c: common_vendor.p({
          placeholder: "请输入真实姓名",
          modelValue: formData.name
        }),
        d: common_vendor.o(($event) => formData.card = $event),
        e: common_vendor.p({
          placeholder: "请输入身份证号",
          modelValue: formData.card
        }),
        f: common_vendor.o(handleSearch),
        g: mediationCase.value !== "" && (formData.name || formData.card)
      }, mediationCase.value !== "" && (formData.name || formData.card) ? {
        h: common_vendor.t(mediationCase.value),
        i: common_vendor.o(navigateToAuth),
        j: common_vendor.o(navigateToAuth)
      } : {}) : {}, {
        k: isAuthenticated.value
      }, isAuthenticated.value ? common_vendor.e({
        l: common_vendor.o(handleSearch),
        m: common_vendor.o(handleSearch),
        n: common_vendor.o(($event) => searchKeyword.value = $event),
        o: common_vendor.p({
          placeholder: "请输入调解案件号进行查询",
          confirmType: "search",
          suffixIcon: "search",
          clearable: true,
          modelValue: searchKeyword.value
        }),
        p: filteredOrderList.value.length > 0
      }, filteredOrderList.value.length > 0 ? {
        q: common_vendor.f(filteredOrderList.value, (order, k0, i0) => {
          var _a;
          return {
            a: common_vendor.t(order.id),
            b: common_vendor.t(order.case_status_cn),
            c: ((_a = statusStyles[order.case_status]) == null ? void 0 : _a.bgColor) || "#999",
            d: common_vendor.t(order.createDate),
            e: order.id,
            f: common_vendor.o(($event) => navigateToDetail(order.id, order.case_status_cn), order.id)
          };
        }),
        r: isLoadingAnimate.value ? 1 : ""
      } : {}, {
        s: isLoading.value && !isRefreshing.value
      }, isLoading.value && !isRefreshing.value ? {} : {}, {
        t: filteredOrderList.value.length === 0 && !isLoading.value
      }, filteredOrderList.value.length === 0 && !isLoading.value ? {
        v: common_assets._imports_0,
        w: common_vendor.t(searchKeyword.value ? "未找到相关调解案件号" : "暂无信息"),
        x: common_vendor.o(handleRefresh)
      } : {}) : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d56de527"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/mediation_query/mediation_query.js.map
