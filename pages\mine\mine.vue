<template>
	<view class="mine-container">
		<!-- 用户信息区域 -->
		<view class="auth">
			<!-- 加载状态骨架屏 -->
			<view v-if="isLoading" class="loading-skeleton">
				<view class="skeleton-user-info">
					<view class="skeleton-avatar"></view>
					<view class="skeleton-details">
						<view class="skeleton-name"></view>
						<view class="skeleton-tip"></view>
					</view>
				</view>
				<view class="skeleton-button"></view>
			</view>
			
			<!-- 用户信息内容 -->
			<view v-else class="user-info-section">
				<view class="avatar-container">
					<image class="avatar" :src="displayAvatar" @error="handleAvatarError"></image>
				</view>
				<view class="user-details">
					<view class="username" @click="!isLoggedIn && navigateTo('/pages/login/login')">
						{{ isLoggedIn ? displayName : '点击登录' }}
					</view>
					
					<!-- 认证状态显示 -->
					<view v-if="isLoggedIn" class="auth-status" :class="authStatus">
						<text v-if="authStatus === 'authenticated'" class="fas fa-check-circle"></text>
						<text v-else class="fas fa-exclamation-circle"></text>
						{{ authStatusText }}
					</view>
					
					<!-- <view v-if="!isLoggedIn" class="login-tip">登录后享受更多服务</view> -->
				</view>
			</view>
			
			<!-- 操作按钮 - 根据认证状态显示 -->
			<!-- 已认证用户 -->
			<!-- <button
				class="auth-button authenticated"
				@click="handleAuthenticatedAction"
				v-if="isLoggedIn && authStatus === 'authenticated' && !isLoading"
				:disabled="isLoading"
			>
				<text class="fas fa-check"></text>
				认证完成 - 查看更多功能
			</button> -->
			
			<!-- 未认证用户 -->
			<!-- <button
				class="auth-button need-auth"
				@click="handleAuth"
				v-if="isLoggedIn && authStatus === 'need_auth' && !isLoading"
				:disabled="isLoading"
			>
				<text class="fas fa-user-check"></text>
				实名认证
			</button> -->
			
			<!-- 未登录用户 -->
			<button
				class="login-button"
				@click="navigateTo('/pages/login/login')"
				v-if="!isLoggedIn && !isLoading"
				:disabled="isLoading"
			>
				立即登录
			</button>
		</view>
		
		<!-- 功能列表 - 根据认证状态显示 -->
		<view class="function-list">
			<!-- 已认证用户可访问的功能 -->
			<view 
				v-if="isLoggedIn || authStatus === 'authenticated'"
				class="function-item"
				@click="navigateTo('/pages/mediation_query/mediation_query')"
			>
				<view class="function-item-left">
					<text class="fas fa-clipboard-list"></text>
					<text class="function-text">我的调解记录</text>
				</view>
				<text class="fas fa-chevron-right"></text>
			</view>
			
			<!-- 未认证用户的功能提示 -->
			<!-- <view 
				v-if="authStatus === 'need_auth'"
				class="function-item disabled"
				@click="showAuthTip"
			>
				<view class="function-item-left">
					<text class="fas fa-clipboard-list"></text>
					<text class="function-text">我的调解记录</text>
					<text class="auth-required-tag">需认证</text>
				</view>
				<text class="fas fa-lock"></text>
			</view> -->
			
			<!-- 通用功能 - 所有用户都可访问 -->
			<view class="function-item" @click="navigateTo('/pages/privacy_policy/privacy_policy')">
				<view class="function-item-left">
					<text class="fas fa-shield-alt"></text>
					<text class="function-text">隐私政策</text>
				</view>
				<text class="fas fa-chevron-right"></text>
			</view>
		</view>
		

		
		<!-- <button
			class="btn-danger"
			@click="handleLogout"
			v-if="isLoggedIn"
		>
			退出登录
		</button> -->
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import userStore, { userComputed } from '@/utils/user-store.js';
import { api } from '@/utils/api.js';

// 响应式数据
const isLoading = ref(false);
const wechatAvatarUrl = ref('');
const wechatNickname = ref('');
const wechatOpenid = ref('');
const detectAuthResult = ref(false); // 用户认证状态：true=已认证，false=未认证

// 计算属性 - 使用用户状态管理
const userInfo = computed(() => userStore.getUserState());
const isLoggedIn = userComputed.isLoggedIn;
const displayName = computed(() => {
	return wechatNickname.value || userComputed.displayName.value || userComputed.wechatUserInfo.nickName || '用户';
});
const displayAvatar = computed(() => {
	return wechatAvatarUrl.value || userComputed.displayAvatar.value || userComputed.wechatUserInfo.avatarUrl || '/static/default-avatar.png';
});
const isVerified = userComputed.isVerified;

// 认证状态计算属性
const authStatus = computed(() => {
	if (!isLoggedIn.value) return 'not_logged_in';
	return detectAuthResult.value ? 'authenticated' : 'need_auth';
});

const authStatusText = computed(() => {
	switch (authStatus.value) {
		case 'authenticated':
			return '已完成实名认证';
		case 'need_auth':
			return '请完成实名认证以使用完整功能';
	}
});

// 页面加载时执行
onMounted(() => {
	// 从本地存储恢复数据
	const storedOpenid = uni.getStorageSync('wechat_openid');
	if (storedOpenid) {
		wechatOpenid.value = storedOpenid;
	}
	
	const storedIsStaff = uni.getStorageSync('detect_auth_result');
	if (typeof storedIsStaff !== 'undefined') {
		detectAuthResult.value = storedIsStaff;
	}
	
	// 检查登录状态并获取用户信息
	checkLoginAndGetUserInfo();
});

// 检查登录状态并获取用户信息
async function checkLoginAndGetUserInfo() {
	if (!isLoggedIn.value) {
		// 未登录状态，清除加载状态
		isLoading.value = false;
		return;
	}

	try {
		isLoading.value = true;
		
		// 调用 /user/user_info/ 接口获取最新用户信息
		const result = await api.user.getUserInfo();
		
		if (result.success && result.data) {
			// 数据映射：将接口返回的特定字段赋值给页面变量
			if (result.data.wechat_avatar_url) {
				wechatAvatarUrl.value = result.data.wechat_avatar_url;
			}
			if (result.data.wechat_nickname) {
				wechatNickname.value = result.data.wechat_nickname;
			}
			if (result.data.wechat_openid) {
				wechatOpenid.value = result.data.wechat_openid;
				// 存储到本地存储
				uni.setStorageSync('wechat_openid', result.data.wechat_openid);
			}
			// 映射认证状态字段
			if (typeof result.data.detect_auth_result !== 'undefined') {
				detectAuthResult.value = result.data.detect_auth_result;
				// 存储认证状态到本地
				uni.setStorageSync('detect_auth_result', result.data.detect_auth_result);
			}
			
			// 更新用户信息到状态管理
			userStore.updateUserInfo(result.data);
			
			// 记录操作日志
			recordOperationLog('查看', '个人中心');
		} else {
			console.warn('获取用户信息失败:', result.message || '未知错误');
		}
	} catch (error) {
		console.error('获取用户信息失败:', error);
		// 如果是401错误，说明token过期，跳转到登录页
		if (error.message && error.message.includes('401')) {
			userStore.clearUserInfo();
			uni.showModal({
				title: '登录过期',
				content: '您的登录已过期，请重新登录',
				showCancel: false,
				success: () => {
					uni.navigateTo({
						url: '/pages/login/login'
					});
				}
			});
		}
	} finally {
		isLoading.value = false;
	}
}



// 处理头像加载错误
function handleAvatarError() {
	// 使用默认头像
	console.log('头像加载失败，使用默认头像');
}

// 记录用户操作日志
async function recordOperationLog(action, target) {
	try {
		if (!isLoggedIn.value) return;
		
		await api.operationLog.recordOperation({
			button_name: action,       // 按钮名称
			button_type: target,       // 按钮类型
			page_url: getCurrentPages()[getCurrentPages().length - 1].route, // 浏览器路径
			page_plate: '个人中心',           // 菜单名称
		});
	} catch (error) {
		console.error('记录操作日志失败:', error);
		// 日志记录失败不影响正常流程
	}
}

// 身份认证处理
/* function handleAuth() {
	if (!isLoggedIn.value) {
		uni.navigateTo({
			url: '/pages/login/login'
		});
		return;
	}

	// 记录操作日志
	recordOperationLog('点击', '身份认证按钮');
	
	// 跳转到身份认证页面
	uni.navigateTo({
		url: '/pages/auth/auth'
	});
} */

// 已认证用户的操作处理
function handleAuthenticatedAction() {
	// 记录操作日志
	recordOperationLog('点击', '已认证功能按钮');
	
	uni.showModal({
		title: '认证完成',
		content: '您已通过身份认证，可以使用完整功能。是否查看更多功能？',
		success: (res) => {
			if (res.confirm) {
				// 可以跳转到功能列表或其他页面
				navigateTo('/pages/mediation_query/mediation_query');
			}
		}
	});
}

// 显示认证提示
function showAuthTip() {
	uni.showModal({
		title: '需要身份认证',
		content: '此功能需要先进行身份认证，是否立即前往认证？',
		success: (res) => {
			if (res.confirm) {
				handleAuth();
			}
		}
	});
}

// 退出登录
/* async function handleLogout() {
	uni.showModal({
		title: '确认退出',
		content: '确定要退出登录吗？',
		success: async (res) => {
			if (res.confirm) {
				try {
					await userStore.logout();
				} catch (error) {
					console.error('退出登录失败:', error);
				}
			}
		}
	});
} */

// 页面导航
function navigateTo(url) {
	if (!isLoggedIn.value && url !== '/pages/login/login') {
		uni.navigateTo({
			url: '/pages/login/login'
		});
		return;
	}

	// 记录操作日志
	const pageName = getPageNameFromUrl(url);
	recordOperationLog('点击', pageName);

	uni.navigateTo({
		url: url
	});
}

// 从URL获取页面名称
function getPageNameFromUrl(url) {
	const pageMap = {
		'/pages/mediation_query/mediation_query': '我的调解记录',
		'/pages/privacy_policy/privacy_policy': '隐私政策',
		'/pages/login/login': '登录页面'
	};
	return pageMap[url] || '未知页面';
}
</script>

<style lang="scss" scoped>
	:root{
		--primary-color:#3B7EEB;
	}
	.mine-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding: 15px 15px 70px;
	}
	.auth{
		background-color: #ffffff;
		border-radius: 10rpx;
		padding: 40rpx 30rpx;
		margin-bottom: 20rpx;
	}
	.user-info-section {
		display: flex;
		align-items: center;
		padding-bottom: 40rpx;
	}
	
	.avatar-container {
		margin-right: 30rpx;
		border-radius: 10rpx;
	}
	
	.avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 50%;
		background-color: #f0f0f0;
	}
	
	.user-details {
		flex: 1;
	}
	
	.username {
		font-size: 36rpx;
		font-weight: 500;
		color: #333;
		margin-bottom: 10rpx;
	}
	
	/* 认证状态样式 */
	.auth-status {
		display: inline-block;
		padding: 2px 0;
		border-radius: 4px;
		font-size: 12px;
		margin-top: 5px;
		
		&.authenticated {
			background-color: #E6F7FF;
			color: var(--primary-color);
			
			.fa-check-circle {
				color: #3B7EEB;
			}
		}
		
		&.need_auth {
			// background-color: #fff7e6;
			color: #979292;
			
			.fa-exclamation-circle {
				color: #979292;
			}
		}
	}
	.auth-button {
		width: 640rpx;
		height: 80rpx;
		line-height: 80rpx;
		color: #fff;
		font-size: 32rpx;
		border-radius: 10rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		
		&.authenticated {
			background-color: #52c41a;
			
			.fas {
				margin-right: 10rpx;
				font-size: 28rpx;
			}
		}
		
		&.need-auth {
			background-color: #3B7EEB;
			
			.fas {
				margin-right: 10rpx;
				font-size: 28rpx;
			}
		}
	}

	.auth-button[disabled] {
		background-color: #cccccc;
	}

	.login-button {
		width: 640rpx;
		height: 80rpx;
		line-height: 80rpx;
		background-color: #07c160;
		color: #fff;
		font-size: 32rpx;
		border-radius: 10rpx;
	}

	.auth-tip {
		font-size: 24rpx;
		color: #ff6b35;
		margin-top: 10rpx;
	}

	.login-tip {
		font-size: 24rpx;
		color: #999;
		margin-top: 10rpx;
	}
	
	.function-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: white;
		border-radius: 24rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
		padding: 30rpx;
		margin-bottom: 30rpx;
		transition: var(--transition-normal);
		border: 2rpx solid rgba(0, 0, 0, 0.03);
		
		&.disabled {
			opacity: 0.6;
			background-color: #f5f5f5;
			
			.function-text {
				color: #999;
			}
			
			.fa-clipboard-list {
				color: #ccc;
			}
			
			.fa-lock {
				color: #fa8c16;
			}
		}
	}
	
	.function-item-left {
		display: flex;
		align-items: center;
		position: relative;
	}
	
	.auth-required-tag {
		background-color: #fa8c16;
		color: white;
		font-size: 20rpx;
		padding: 2rpx 8rpx;
		border-radius: 8rpx;
		margin-left: 15rpx;
	}
	
	.function-text {
		font-size: 30rpx;
		color: #333;
		margin-left: 20rpx;
	}
	
	.fa-clipboard-list,.fa-shield-alt {
		font-size: 40rpx;
		color: #3b7eeb;
	}
	
	.fa-chevron-right {
		color: #999;
	}
	.btn-danger {
		background-color: #f5222d;
		color: white;
		margin-top: 20rpx;
		display: block;
		width: 100%;
	}
	
	/* 加载骨架屏样式 */
	.loading-skeleton {
		.skeleton-user-info {
			display: flex;
			align-items: center;
			padding-bottom: 40rpx;
		}
		
		.skeleton-avatar {
			width: 120rpx;
			height: 120rpx;
			border-radius: 50%;
			background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
			background-size: 200% 100%;
			animation: skeleton-loading 1.5s infinite;
			margin-right: 30rpx;
		}
		
		.skeleton-details {
			flex: 1;
		}
		
		.skeleton-name {
			width: 200rpx;
			height: 36rpx;
			background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
			background-size: 200% 100%;
			animation: skeleton-loading 1.5s infinite;
			border-radius: 4rpx;
			margin-bottom: 20rpx;
		}
		
		.skeleton-tip {
			width: 300rpx;
			height: 24rpx;
			background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
			background-size: 200% 100%;
			animation: skeleton-loading 1.5s infinite;
			border-radius: 4rpx;
		}
		
		.skeleton-button {
			width: 640rpx;
			height: 80rpx;
			background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
			background-size: 200% 100%;
			animation: skeleton-loading 1.5s infinite;
			border-radius: 10rpx;
		}
	}
	
	@keyframes skeleton-loading {
		0% {
			background-position: -200% 0;
		}
		100% {
			background-position: 200% 0;
		}
	}
	

</style> 